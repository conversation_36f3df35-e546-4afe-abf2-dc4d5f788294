import apiClient from './client';

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  full_name: string;
  role: 'student' | 'tutor' | 'admin';
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
}

export interface ProfileCompleteRequest {
  other_name?: string;
  gender: 'male' | 'female' | 'other';
  phone_number: string;
  department: string;  // Legacy field, kept for backward compatibility
  department_id?: number;
  level: string;
  date_of_birth: string;
  state_of_origin: string;
  institution_id?: number;
}

export interface User {
  id: number;
  email: string;
  full_name: string;
  other_name?: string;
  role: 'student' | 'tutor' | 'admin';
  is_active: boolean;
  email_verified: boolean;
  gender?: 'male' | 'female' | 'other';
  phone_number?: string;
  department?: string;  // Legacy field, kept for backward compatibility
  department_id?: number;
  level?: string;
  date_of_birth?: string;
  state_of_origin?: string;
  profile_picture_url?: string;
  profile_completed: boolean;
  institution_id?: number;
  created_at: string;
  updated_at: string;
}

export const login = async (data: LoginRequest): Promise<AuthResponse> => {
  try {
    console.log('Login attempt with username:', data.username);

    // Create URLSearchParams for x-www-form-urlencoded format
    const formData = new URLSearchParams();
    formData.append('username', data.username);
    formData.append('password', data.password);

    console.log('Sending login request to API...');
    const response = await apiClient.post<AuthResponse>('/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      timeout: 5000, // 5 second timeout for login specifically
    });

    console.log('Login successful, received token:', response.data);

    // Store the token in localStorage
    if (response.data && response.data.access_token) {
      localStorage.setItem('token', response.data.access_token);
      console.log('Token stored in localStorage');
    } else {
      console.error('No access token in response');
    }

    return response.data;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

export const register = async (data: RegisterRequest): Promise<User> => {
  try {
    console.log('Sending registration request to API:', data);
    const response = await apiClient.post<User>('/auth/register', data);
    console.log('Registration API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Registration API error:', error);
    throw error;
  }
};

export const getCurrentUser = async (): Promise<User> => {
  try {
    console.log('Fetching current user data...');

    // Ensure we have a valid token
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('No authentication token found during getCurrentUser');
      throw new Error('You are not authenticated. Please log in again.');
    }

    // Log the headers being sent
    console.log('Request headers for getCurrentUser:', {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token.substring(0, 10)}...` // Only show part of the token for security
    });

    const response = await apiClient.get<User>('/users/me');
    console.log('Current user data received:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching current user:', error);

    // Enhanced error logging
    if (error.response) {
      console.error('Error response status:', error.response.status);
      console.error('Error response data:', error.response.data);

      // Handle specific error codes
      if (error.response.status === 401) {
        console.error('401 Unauthorized: Invalid or expired token');
        localStorage.removeItem('token'); // Clear the invalid token
        localStorage.removeItem('user');
        throw new Error('Your session has expired. Please log in again.');
      } else if (error.response.status === 403) {
        console.error('403 Forbidden: You do not have permission to access this resource');
        throw new Error('You do not have permission to access this resource.');
      }
    }

    throw error;
  }
};

export const completeProfile = async (data: ProfileCompleteRequest): Promise<User> => {
  try {
    console.log('Sending profile completion request to API:', data);

    // Ensure we have a valid token
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('No authentication token found');
      throw new Error('You are not authenticated. Please log in again.');
    }

    // Log the headers being sent
    console.log('Request headers:', {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    });

    // Make the API call
    const response = await apiClient.post<User>('/auth/complete-profile', data);
    console.log('Profile completion API response:', response.data);

    // Update the stored user data
    const userData = await getCurrentUser();
    localStorage.setItem('user', JSON.stringify(userData));

    return response.data;
  } catch (error) {
    console.error('Profile completion API error:', error);

    // Enhanced error logging
    if (error.response) {
      console.error('Error response status:', error.response.status);
      console.error('Error response data:', error.response.data);
      console.error('Error response headers:', error.response.headers);

      // Handle specific error codes
      if (error.response.status === 403) {
        console.error('403 Forbidden: You do not have permission to complete your profile.');
        throw new Error('You do not have permission to complete your profile. Please contact support.');
      }
    }

    throw error;
  }
};

// Email verification interfaces
export interface EmailVerificationRequest {
  email: string;
}

export interface EmailVerificationConfirm {
  token: string;
}

export interface EmailVerificationResponse {
  message: string;
  success: boolean;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirm {
  token: string;
  new_password: string;
}

export interface PasswordResetResponse {
  message: string;
  success: boolean;
}

// Email verification API functions
export const verifyEmail = async (token: string): Promise<EmailVerificationResponse> => {
  try {
    console.log('Verifying email with token:', token);
    const response = await apiClient.post<EmailVerificationResponse>('/auth/verify-email', { token });
    console.log('Email verification API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Email verification API error:', error);
    throw error;
  }
};

export const resendVerificationEmail = async (email: string): Promise<EmailVerificationResponse> => {
  try {
    console.log('Resending verification email to:', email);
    const response = await apiClient.post<EmailVerificationResponse>('/auth/resend-verification', { email });
    console.log('Resend verification API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Resend verification API error:', error);
    throw error;
  }
};

// Password reset API functions
export const forgotPassword = async (data: PasswordResetRequest): Promise<PasswordResetResponse> => {
  try {
    console.log('Requesting password reset for:', data.email);
    const response = await apiClient.post<PasswordResetResponse>('/auth/forgot-password', data);
    console.log('Forgot password API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Forgot password API error:', error);
    throw error;
  }
};

export const resetPassword = async (data: PasswordResetConfirm): Promise<PasswordResetResponse> => {
  try {
    console.log('Resetting password with token');
    const response = await apiClient.post<PasswordResetResponse>('/auth/reset-password', data);
    console.log('Reset password API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Reset password API error:', error);
    throw error;
  }
};
