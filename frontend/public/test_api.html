<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; background: #f9f9f9; }
        .error { background: #ffebee; border-color: #f44336; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>API Test Page</h1>
    
    <button onclick="testBackendConnectivity()">Test Backend Connectivity</button>
    <button onclick="testSchoolsAPI()">Test Schools API</button>
    <button onclick="testDepartmentsAPI()">Test Departments API (School ID 1)</button>
    <button onclick="clearResults()">Clear Results</button>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        
        function addResult(title, content, isError = false) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${isError ? 'error' : 'success'}`;
            resultDiv.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testSchoolsAPI() {
            try {
                console.log('Testing schools API...');
                const response = await fetch(`${API_BASE}/schools/public`);
                console.log('Schools API response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('Schools API data:', data);
                
                addResult(
                    'Schools API - SUCCESS', 
                    `Status: ${response.status}\nCount: ${data.length}\nData: ${JSON.stringify(data, null, 2)}`
                );
            } catch (error) {
                console.error('Schools API error:', error);
                addResult('Schools API - ERROR', error.message, true);
            }
        }
        
        async function testDepartmentsAPI() {
            try {
                console.log('Testing departments API...');
                const response = await fetch(`${API_BASE}/departments/public/school/1`);
                console.log('Departments API response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('Departments API data:', data);
                
                addResult(
                    'Departments API - SUCCESS', 
                    `Status: ${response.status}\nCount: ${data.length}\nData: ${JSON.stringify(data, null, 2)}`
                );
            } catch (error) {
                console.error('Departments API error:', error);
                addResult('Departments API - ERROR', error.message, true);
            }
        }
        
        // Test backend connectivity first
        async function testBackendConnectivity() {
            try {
                const response = await fetch(`${API_BASE}/docs`);
                addResult('Backend Connectivity', `Status: ${response.status} - Backend is ${response.ok ? 'UP' : 'DOWN'}`);
            } catch (error) {
                addResult('Backend Connectivity', `Backend is DOWN - ${error.message}`, true);
            }
        }

        // Auto-test on page load
        window.onload = function() {
            console.log('Page loaded, testing APIs...');
            testBackendConnectivity();
            setTimeout(testSchoolsAPI, 1000);
        };
    </script>
</body>
</html>
