from datetime import timedelta
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app import schemas
from app.api import deps
from app.core import security
from app.core.config import settings
from app.services import user_service

router = APIRouter()


@router.post("/login", response_model=schemas.Token)
def login_access_token(
    db: Session = Depends(deps.get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    user = user_service.authenticate(
        db, email=form_data.username, password=form_data.password
    )
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    elif not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    elif not user.email_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email not verified. Please check your email and verify your account before logging in.",
            headers={"X-Email-Verification-Required": "true"},
        )
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    return {
        "access_token": security.create_access_token(
            user.id, expires_delta=access_token_expires
        ),
        "token_type": "bearer",
    }


@router.post("/register", response_model=schemas.User)
def register_user(
    *,
    db: Session = Depends(deps.get_db),
    user_in: schemas.UserCreate,
) -> Any:
    """
    Register a new user and send verification email
    """
    user = user_service.get_by_email(db, email=user_in.email)
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="A user with this email already exists",
        )
    user = user_service.create_with_verification(db, obj_in=user_in)
    return user


@router.post("/complete-profile", response_model=schemas.User)
def complete_profile(
    *,
    db: Session = Depends(deps.get_db),
    profile_data: schemas.ProfileComplete,
    current_user: schemas.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Complete user profile with additional information
    """
    # Check if email is verified before allowing profile completion
    if not current_user.email_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email must be verified before completing profile. Please check your email and verify your account.",
            headers={"X-Email-Verification-Required": "true"},
        )

    if current_user.profile_completed:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Profile already completed",
        )

    # Update user with profile data
    update_data = profile_data.model_dump()
    update_data["profile_completed"] = True

    user = user_service.update(db, db_obj=current_user, obj_in=update_data)
    return user


@router.post("/verify-email", response_model=schemas.EmailVerificationResponse)
def verify_email(
    *,
    db: Session = Depends(deps.get_db),
    verification_data: schemas.EmailVerificationConfirm,
) -> Any:
    """
    Verify email address using verification token
    """
    user = user_service.verify_email(db, token=verification_data.token)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired verification token",
        )

    return {
        "message": "Email verified successfully! You can now log in to your account.",
        "success": True
    }


@router.post("/resend-verification", response_model=schemas.EmailVerificationResponse)
def resend_verification_email(
    *,
    db: Session = Depends(deps.get_db),
    email_data: schemas.EmailVerificationRequest,
) -> Any:
    """
    Resend email verification email
    """
    user = user_service.get_by_email(db, email=email_data.email)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User with this email not found",
        )

    if user.email_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email is already verified",
        )

    success = user_service.resend_verification_email(db, email=email_data.email)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send verification email",
        )

    return {
        "message": "Verification email sent successfully! Please check your email.",
        "success": True
    }


@router.post("/test-email", response_model=schemas.EmailVerificationResponse)
def test_email_sending(
    *,
    db: Session = Depends(deps.get_db),
    email_data: schemas.EmailVerificationRequest,
) -> Any:
    """
    Test email sending functionality (development only)
    """
    from app.services.email_service import send_verification_email

    # Generate a test token
    test_token = "test_token_12345"

    # Send test verification email
    success = send_verification_email(
        email_to=email_data.email,
        verification_token=test_token,
        user_name="Test User"
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send test email",
        )

    return {
        "message": f"Test email sent successfully to {email_data.email}!",
        "success": True
    }


@router.post("/forgot-password", response_model=schemas.PasswordResetResponse)
def forgot_password(
    *,
    db: Session = Depends(deps.get_db),
    password_reset_data: schemas.PasswordResetRequest,
) -> Any:
    """
    Request password reset email
    """
    success = user_service.request_password_reset(db, email=password_reset_data.email)

    # Always return success for security (don't reveal if email exists)
    return {
        "message": "If an account with this email exists, you will receive a password reset link shortly.",
        "success": True
    }


@router.post("/reset-password", response_model=schemas.PasswordResetResponse)
def reset_password(
    *,
    db: Session = Depends(deps.get_db),
    password_reset_data: schemas.PasswordResetConfirm,
) -> Any:
    """
    Reset password using reset token
    """
    user = user_service.reset_password(
        db,
        token=password_reset_data.token,
        new_password=password_reset_data.new_password
    )

    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired password reset token",
        )

    return {
        "message": "Password reset successfully! You can now log in with your new password.",
        "success": True
    }
