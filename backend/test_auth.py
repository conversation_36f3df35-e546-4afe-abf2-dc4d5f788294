#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app import models
from app.services import user_service
from app import schemas
from app.core.security import create_access_token
from app.models.user import UserR<PERSON>, Gender
from datetime import datetime, timed<PERSON><PERSON>

def create_test_user():
    """Create a test user for profile completion testing"""
    db = SessionLocal()
    
    try:
        print("=== CREATING TEST USER FOR PROFILE COMPLETION ===")
        
        # Check if test user already exists
        test_email = "<EMAIL>"
        existing_user = user_service.get_by_email(db, email=test_email)
        
        if existing_user:
            print(f"Test user already exists: {existing_user.email}")
            print(f"Email verified: {existing_user.email_verified}")
            print(f"Profile completed: {existing_user.profile_completed}")
            print(f"Is active: {existing_user.is_active}")
            
            # Update user to be email verified but profile not completed
            update_data = {
                "email_verified": True,
                "profile_completed": False,
                "email_verification_token": None,
                "email_verification_token_expires": None
            }
            user = user_service.update(db, db_obj=existing_user, obj_in=update_data)
            print("Updated existing user for testing")
        else:
            # Create new test user
            user_in = schemas.UserCreate(
                email=test_email,
                password="testpassword123",
                full_name="Test User",
                role=UserRole.STUDENT,
                is_active=True,
            )
            
            # Create user directly (not with verification)
            user = user_service.create(db, obj_in=user_in)
            
            # Update to be email verified but profile not completed
            update_data = {
                "email_verified": True,
                "profile_completed": False
            }
            user = user_service.update(db, db_obj=user, obj_in=update_data)
            print(f"Created new test user: {user.email}")
        
        # Generate access token
        access_token = create_access_token(user.id)
        
        print(f"\n=== TEST USER DETAILS ===")
        print(f"Email: {user.email}")
        print(f"Password: testpassword123")
        print(f"Email verified: {user.email_verified}")
        print(f"Profile completed: {user.profile_completed}")
        print(f"Is active: {user.is_active}")
        print(f"Role: {user.role}")
        print(f"User ID: {user.id}")
        
        print(f"\n=== ACCESS TOKEN ===")
        print(f"Bearer {access_token}")
        
        print(f"\n=== CURL TEST COMMANDS ===")
        print(f"# Test authentication:")
        print(f'curl -X GET "http://localhost:8000/api/v1/users/me" -H "Authorization: Bearer {access_token}"')
        
        print(f"\n# Test profile completion:")
        print(f'curl -X POST "http://localhost:8000/api/v1/auth/complete-profile" \\')
        print(f'  -H "Authorization: Bearer {access_token}" \\')
        print(f'  -H "Content-Type: application/json" \\')
        print(f'  -d \'{{"gender": "male", "phone_number": "08012345678", "department": "Computer Science", "level": "100", "date_of_birth": "2000-01-01", "state_of_origin": "Lagos", "institution_id": 1, "department_id": 16}}\'')
        
        return user, access_token
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        db.rollback()
        return None, None
    finally:
        db.close()

if __name__ == "__main__":
    create_test_user()
